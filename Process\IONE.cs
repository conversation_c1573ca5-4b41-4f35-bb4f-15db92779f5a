using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IONE : IProcess
    {
        private readonly string SearchMainPage = "https://ecomm.one-line.com/one-ecom";

        public IONE() : base("ONE", "ONEY") { }


        public override List<IProcessItem> Run(string searchKey, string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "点击", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "输入", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "点击", IProcessType.OPERATE);
            IProcessItem fifPro = new IProcessItem(4, "批量处理集装箱", IProcessType.OPERATE);
            IProcessItem sixPro = new IProcessItem(5, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "let trackTab = document.getElementById('quick-action_tracking-box');trackTab.click();";
            thiPro.JScript = "let input = document.querySelector('textarea');if(input){input.focus(); document.execCommand('insertText', false, '" + searchKey + "');}";
            fouPro.JScript = "var buttons = document.querySelectorAll('button');" +
                        "buttons.forEach(function(button) {" +
                        "button.classList.forEach(function(clazz){" +
                        "if(clazz.includes('TrackInputAreaContent_track-button')){" +
                        "button.click();" +
                        "return" +
                        "}" +
                        "})" +
                        "});";

            // 批量处理所有集装箱：依次点击每个集装箱并收集信息
            fifPro.JScript = @"(function() {
                        const iframe = document.getElementById('IframeCurrentEcom');
                        if(iframe){
                            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                            const table = iframeDocument.getElementById('main-grid');
                            if (table) {
                                // 等待表格加载完成
                                const observer = new MutationObserver((mutationsList, observer) => {
                                    if (table.rows.length >= 2) {
                                        observer.disconnect();
                                        processAllContainers(table);
                                    }
                                });
                                observer.observe(table, { childList: true, subtree: true });
                                if (table.rows.length >= 2) {
                                    observer.disconnect();
                                    processAllContainers(table);
                                }
                            }

                            async function processAllContainers(table) {
                                window.allContainerData = [];

                                // 收集所有集装箱链接
                                const containerLinks = [];
                                for(let i = 1; i < table.rows.length; i++) {
                                    const row = table.rows[i];
                                    const link = row.querySelector('a');
                                    if(link) {
                                        containerLinks.push({
                                            element: link,
                                            text: link.textContent.trim(),
                                            index: i
                                        });
                                    }
                                }


                                // 依次点击每个集装箱并收集详细信息
                                for(let i = 0; i < containerLinks.length; i++) {
                                    const linkInfo = containerLinks[i];

                                    // 点击集装箱链接
                                    linkInfo.element.click();

                                    // 等待详细信息加载
                                    await new Promise(resolve => setTimeout(resolve, 1000));

                                    // 收集当前集装箱的详细信息
                                    const detailInfoDiv = iframeDocument.getElementById('detailInfo');
                                    const containerDetail = {
                                        containerNo: linkInfo.text,
                                        detailHtml: detailInfoDiv ? detailInfoDiv.innerHTML : '',
                                        index: i
                                    };

                                    window.allContainerData.push(containerDetail);
                                }

                            }
                        }
                    })()";

            sixPro.JScript = @"(function() {
                        // 返回所有收集到的集装箱数据
                        if (window.allContainerData && window.allContainerData.length > 0) {
                            // 使用特殊分隔符连接数据，避免JSON解析问题
                            let result = '';
                            for(let i = 0; i < window.allContainerData.length; i++) {
                                const container = window.allContainerData[i];
                                result += '===CONTAINER_START===' + container.containerNo + '===CONTAINER_START===';
                                result += container.detailHtml;
                                result += '===CONTAINER_END===' + container.containerNo + '===CONTAINER_END===';
                                if(i < window.allContainerData.length - 1) {
                                    result += '===NEXT_CONTAINER===';
                                }
                            }
                            return result;
                        } else {
                            // 如果没有收集到数据，返回当前iframe的detailInfo内容
                            var iframe = document.getElementById('IframeCurrentEcom');
                            if (iframe) {
                                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                                const detailInfoDiv = iframeDocument.getElementById('detailInfo');
                                return detailInfoDiv ? detailInfoDiv.innerHTML : '';
                            }
                            return '';
                        }
                    })()";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);
            processList.Add(fifPro);
            processList.Add(sixPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();

                // 检查是否是分隔符格式的多集装箱数据
                result = ProcessDelimitedContainers(resultString);

                return result;
            }
            catch (Exception exc)
            {
                throw new Exception($"处理结果数据时出错: {exc.Message}");
            }
        }

        private IResult ProcessDelimitedContainers(string delimitedData)
        {
            try
            {
                IResult result = new IResult();
                BindingList<IResultContainer> allContainers = new BindingList<IResultContainer>();

                // 按分隔符分割数据
                string[] containerSections = delimitedData.Split(new string[] { "===NEXT_CONTAINER===" }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string section in containerSections)
                {
                    try
                    {
                        // 提取集装箱号
                        string containerNo = ExtractContainerNoFromSection(section);

                        // 提取HTML内容
                        string htmlContent = ExtractHtmlFromSection(section, containerNo);

                        if (!string.IsNullOrEmpty(containerNo) && !string.IsNullOrEmpty(htmlContent))
                        {
                            // 从HTML中提取集装箱信息
                            var containerInfo = ExtractContainerInfo(htmlContent, containerNo);
                            if (containerInfo != null)
                            {
                                allContainers.Add(containerInfo);
                            }

                            // 提取ETA信息（使用第一个有效的ETA）
                            if (string.IsNullOrEmpty(result.ETA))
                            {
                                try
                                {
                                    result.ETA = this.GetETA(htmlContent);
                                }
                                catch
                                {
                                    // 忽略ETA提取错误，继续处理其他集装箱
                                }
                            }
                        }
                    }
                    catch (Exception exc)
                    {
                        // 记录单个集装箱处理错误，但继续处理其他集装箱
                        System.Diagnostics.Debug.WriteLine($"处理集装箱段落时出错: {exc.Message}");
                    }
                }

                result.ContainerList = allContainers;
                return result;
            }
            catch (Exception exc)
            {
                throw new Exception($"处理分隔符格式数据时出错: {exc.Message}");
            }
        }

        private string ExtractContainerNoFromSection(string section)
        {
            try
            {
                // 查找 ===CONTAINER_START===集装箱号===CONTAINER_START=== 模式
                string pattern = @"===CONTAINER_START===([^=]+)===CONTAINER_START===";
                Match match = Regex.Match(section, pattern);

                if (match.Success)
                {
                    return match.Groups[1].Value.Trim();
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        private string ExtractHtmlFromSection(string section, string containerNo)
        {
            try
            {
                // 查找集装箱号之间的HTML内容
                string startPattern = $"===CONTAINER_START==={containerNo}===CONTAINER_START===";
                string endPattern = $"===CONTAINER_END==={containerNo}===CONTAINER_END===";

                int startIndex = section.IndexOf(startPattern);
                int endIndex = section.IndexOf(endPattern);

                if (startIndex >= 0 && endIndex > startIndex)
                {
                    startIndex += startPattern.Length;
                    return section.Substring(startIndex, endIndex - startIndex);
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        private IResultContainer ExtractContainerInfo(string html, string containerNo)
        {
            try
            {
                IResultContainer container = new IResultContainer();

                // 提取集装箱相关的时间信息
                string pickupDate = "";
                string unloadingDate = "";
                string returnDate = "";

                // 直接查找id="detail"的表格，不限制在detailInfo div内
                string tablePattern = @"<table[^>]*id=""detail""[^>]*>(.*?)</table>";
                Match tableMatch = Regex.Match(html, tablePattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);

                if (tableMatch.Success)
                {
                    string tableContent = tableMatch.Value;

                    // 解析表格行
                    string rowPattern = @"<tr[^>]*>(.*?)</tr>";
                    MatchCollection rowMatches = Regex.Matches(tableContent, rowPattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);

                    foreach (Match rowMatch in rowMatches)
                    {
                        string rowContent = rowMatch.Value;

                        // 提取单元格内容
                        string cellPattern = @"<td[^>]*>(.*?)</td>";
                        MatchCollection cellMatches = Regex.Matches(rowContent, cellPattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);

                        if (cellMatches.Count >= 4)
                        {
                            string statusCell = cellMatches[1].Value;
                            string dateCell = cellMatches[3].Value;

                            // 清理HTML标签获取纯文本
                            string status = Regex.Replace(statusCell, @"<[^>]+>", "").Trim();
                            string dateText = Regex.Replace(dateCell, @"<[^>]+>", "").Trim();

                            // 提取日期 (格式: 2025-07-22 19:07)
                            string datePattern = @"(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2})";
                            Match dateMatch = Regex.Match(dateText, datePattern);

                            if (dateMatch.Success)
                            {
                                string extractedDate = dateMatch.Groups[1].Value;

                                // 根据状态分类日期
                                if (status.Contains("Empty Container Returned from Customer"))
                                {
                                    returnDate = extractedDate;
                                }
                                else if (status.Contains("Gate Out from Inbound Terminal for Delivery to Consignee") ||
                                         status.Contains("Gate Out from Inbound Terminal for Delivery"))
                                {
                                    pickupDate = extractedDate;
                                }
                                else if (status.Contains("Unloaded from"))
                                {
                                    unloadingDate = extractedDate;
                                }
                            }
                        }
                    }
                }

                // 使用SetNewContainerItem方法设置集装箱信息
                container.SetNewContainerItem(containerNo, pickupDate, unloadingDate, returnDate);

                return container;
            }
            catch (Exception exc)
            {
                throw new Exception($"提取集装箱 {containerNo} 信息时出错: {exc.Message}");
            }
        }

        private string GetETA(string result)
        {
            try
            {
                string contentPattern = @"<table[^>]*id=""[^""]*sailing""[^>]*>(.*?)<\/table>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string trPattern = @"<tr>(.*?)<\/tr>";

                MatchCollection trMatches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);

                if (trMatches.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = trMatches[trMatches.Count - 1].Value;

                string tdPattern = @"<td[^>]*>(.*?)<\/td>";

                MatchCollection tdMatches = Regex.Matches(matchVal, tdPattern, RegexOptions.Singleline);

                if (tdMatches.Count < 5)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = tdMatches[4].Value;
                string etaPattern = @"<\/span>(.*?)<\/td>";

                MatchCollection etaMatches = Regex.Matches(matchVal, etaPattern, RegexOptions.Singleline);

                if (etaMatches.Count <= 0 || etaMatches[0].Groups.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = etaMatches[0].Groups[1].Value;

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                if (DateTime.TryParse(inputDate, out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }



    }
}
